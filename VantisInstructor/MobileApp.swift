//
//  MobileApp.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI
import UserNotifications

@main
struct MobileApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var authViewModel = AuthViewModel()
    @StateObject private var configService = ConfigurationService.shared
    @StateObject private var notificationService = NotificationService.shared
    @StateObject private var syncService = SyncService.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(authViewModel)
                .environmentObject(AuthenticationStateManager.shared)
                .environmentObject(configService)
                .environmentObject(notificationService)
                .environmentObject(syncService)
                .onAppear {
                    setupApp()
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
                    handleAppBecomeActive()
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)) { _ in
                    handleAppEnterBackground()
                }
                .errorAlert()
        }
    }
    
    private func setupApp() {
        // Check authentication state on app startup
        print("🔐 MobileApp: Checking authentication state on startup")
        authViewModel.checkAuthState()

        // Register custom fonts
        FontRegistration.registerFonts()

        // Load configuration
        Task {
            await configService.loadConfiguration()
        }

        // Setup analytics
        AnalyticsService.shared.startNewSession()

        // Track app launch
        let launchTime = Date().timeIntervalSince(appDelegate.launchTime)
        AnalyticsService.shared.trackAppLaunch(launchTime: launchTime)

        // Request notification permissions if needed
        Task {
            await NotificationService.shared.requestPermission()
        }

        // Don't start sync service automatically - only after manual login
        print("🔐 MobileApp: App setup complete - auth state: \(authViewModel.isAuthenticated)")
    }
    
    private func handleAppBecomeActive() {
        // Update badge count
        NotificationService.shared.updateBadgeCount()

        // Resume sync if authenticated
        if authViewModel.isAuthenticated {
            syncService.startPeriodicSync()
        }

        // Track app foreground
        AnalyticsService.shared.track(event: "app_foreground")
    }
    
    private func handleAppEnterBackground() {
        // Stop sync service
        syncService.stopPeriodicSync()

        // Track app background
        AnalyticsService.shared.track(event: "app_background")
    }

    // MARK: - Force Clean Auth State
    private func forceCleanAuthState() {
        print("🧹 MobileApp: Force cleaning all auth state...")

        // Clear AuthViewModel
        Task {
            await authViewModel.logout()
        }

        // Clear TokenManager
        TokenManager.shared.clearToken()

        // Clear UserDefaults auth data
        let authKeys = [
            "linkx_access_token",
            "linkx_current_user",
            "saved_email",
            "access_token",
            "refresh_token"
        ]

        for key in authKeys {
            UserDefaults.standard.removeObject(forKey: key)
        }

        // Clear Keychain auth data
        let keychainManager = KeychainManager.shared
        try? keychainManager.deleteAccessToken()
        try? keychainManager.deleteRefreshToken()
        try? keychainManager.delete(key: "saved_credentials")

        print("🧹 MobileApp: Force clean auth state complete")
    }
}

// MARK: - App Delegate
class AppDelegate: NSObject, UIApplicationDelegate {
    let launchTime = Date()
    
    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        // Setup logging
        Logger.shared.info("App launched", category: .general)
        
        // Setup appearance
        setupAppearance()
        
        // Register for remote notifications
        // TODO: Enable when push notifications are properly configured
        // application.registerForRemoteNotifications()
        
        return true
    }
    
    // MARK: - Remote Notifications
    func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
        NotificationService.shared.setDeviceToken(deviceToken)
    }
    
    func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {
        Logger.shared.error("Failed to register for remote notifications", error: error, category: .general)
    }
    
    // MARK: - Background App Refresh
    func application(
        _ application: UIApplication,
        performFetchWithCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
    ) {
        Task {
            await SyncService.shared.performBackgroundSync()
            completionHandler(.newData)
        }
    }
    
    // MARK: - App Lifecycle
    func applicationWillTerminate(_ application: UIApplication) {
        Logger.shared.info("App will terminate", category: .general)
        AnalyticsService.shared.endSession()
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        Logger.shared.info("App entered background", category: .general)
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        Logger.shared.info("App will enter foreground", category: .general)
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        Logger.shared.info("App became active", category: .general)
    }
    
    // MARK: - Setup
    private func setupAppearance() {
        // Configure navigation bar appearance
        let navBarAppearance = UINavigationBarAppearance()
        navBarAppearance.configureWithOpaqueBackground()
        navBarAppearance.backgroundColor = UIColor(AppConstants.Colors.surface)
        navBarAppearance.titleTextAttributes = [
            .foregroundColor: UIColor(AppConstants.Colors.textPrimary)
        ]
        
        UINavigationBar.appearance().standardAppearance = navBarAppearance
        UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
        
        // Hide default tab bar since we're using custom modern tab bar
        UITabBar.appearance().isHidden = true
        
        // Configure tint colors
        UIView.appearance().tintColor = UIColor(AppConstants.Colors.primary)
    }
}

// MARK: - Content View
struct ContentView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var authStateManager: AuthenticationStateManager
    @EnvironmentObject var configService: ConfigurationService
    @State private var showMaintenanceView = false
    @State private var showUpdateView = false
    
    var body: some View {
        Group {
            switch authStateManager.authenticationState {
            case .checking:
                LoadingView()
                    .onAppear {
                        print("🔐 ContentView: Showing LoadingView - checking auth state")
                    }
            case .authenticated:
                if showMaintenanceView {
                    MaintenanceView()
                } else if showUpdateView {
                    UpdateRequiredView()
                } else {
                    RoleBasedTabView()
                        .onAppear {
                            print("🔐 ContentView: Showing RoleBasedTabView - user is authenticated")
                        }
                }
            case .unauthenticated, .error:
                AuthenticationView()
                    .onAppear {
                        print("🔐 ContentView: Showing AuthenticationView - user is NOT authenticated")
                    }
            }
        }
        .preferredColorScheme(.light) // Force light mode only
        .onReceive(authStateManager.$authenticationState) { state in
            print("🔐 ContentView: Received authenticationState change = \(state)")
        }
        .onReceive(authStateManager.$currentUser) { user in
            print("🔐 ContentView: Received currentUser change = \(user?.displayName ?? "nil")")
        }
        .onReceive(configService.$maintenanceMode) { maintenance in
            showMaintenanceView = maintenance?.isCurrentlyActive == true &&
                                 maintenance?.isVersionAllowed == false
        }
        // .debugMenu() // Triple tap to show debug menu in DEBUG builds - DISABLED
        .onReceive(configService.$appConfig) { config in
            showUpdateView = config?.isForceUpdateRequired == true
        }
    }
}

// MARK: - Maintenance View
struct MaintenanceView: View {
    @EnvironmentObject var configService: ConfigurationService
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "wrench.and.screwdriver.fill")
                .font(.system(size: 80))
                .foregroundColor(AppConstants.Colors.warning)
            
            VStack(spacing: 16) {
                Text(configService.maintenanceMode?.title ?? "Maintenance Mode")
                    .font(AppConstants.Typography.title)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)

                Text(configService.maintenanceMode?.message ?? "We're currently performing maintenance. Please try again later.")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            
            if let endTime = configService.maintenanceMode?.endTime {
                Text("Expected completion: \(endTime.formatted(date: .abbreviated, time: .shortened))")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textTertiary)
            }
            
            Spacer()
            
            Button("Retry") {
                Task {
                    await configService.loadConfiguration()
                }
            }
            .primaryButtonStyle()
            .padding(.horizontal, 32)
        }
        .background(AppConstants.Colors.background.ignoresSafeArea())
    }
}

// MARK: - Update Required View
struct UpdateRequiredView: View {
    @EnvironmentObject var configService: ConfigurationService
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "arrow.clockwise.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(AppConstants.Colors.primary)
            
            VStack(spacing: 16) {
                Text("Update Required")
                    .font(AppConstants.Typography.title)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("A new version of the app is required to continue. Please update to the latest version from the App Store.")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            
            Spacer()
            
            Button("Update Now") {
                if let url = URL(string: AppConstants.URLs.appStore) {
                    UIApplication.shared.open(url)
                }
            }
            .primaryButtonStyle()
            .padding(.horizontal, 32)
        }
        .background(AppConstants.Colors.background.ignoresSafeArea())
    }
}

// MARK: - Main Tab View
struct MainTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var notificationsViewModel = NotificationsViewModel()

    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Home", icon: "house.circle", selectedIcon: "house.circle.fill"),
            TabItem(id: 1, title: "Bài tập", icon: "list.clipboard", selectedIcon: "list.clipboard.fill"),
            TabItem(id: 2, title: "Lịch dạy", icon: "calendar.circle", selectedIcon: "calendar.circle.fill"),
            TabItem(id: 3, title: "History", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
            TabItem(id: 4, title: "Profile", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }

    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            Group {
                switch selectedTab {
                case 0:
                    HomeView()
                        .environmentObject(notificationsViewModel)
                case 1:
                    NavigationView {
                        QuizManagementView()
                            .environmentObject(authViewModel)
                    }
                case 2:
                    NavigationView {
                        ClassListView()
                    }
                case 3:
                    TransactionHistoryView()
                case 4:
                    ProfileView()
                        .environmentObject(authViewModel)
                default:
                    HomeView()
                        .environmentObject(notificationsViewModel)
                }
            }
        }
    }
}

// MARK: - Authentication View
struct AuthenticationView: View {
    var body: some View {
        NavigationView {
            LoginView()
        }
    }
}

// MARK: - Loading View
struct LoadingView: View {
    var body: some View {
        ZStack {
            AppConstants.Colors.background
                .ignoresSafeArea()

            VStack(spacing: 20) {
                ProgressView()
                    .scaleEffect(1.5)
                    .tint(AppConstants.Colors.primary)

                Text("YourApp")
                    .font(AppConstants.Typography.largeTitle)
                    .foregroundColor(AppConstants.Colors.primary)

                Text("Loading...")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
    }
}
