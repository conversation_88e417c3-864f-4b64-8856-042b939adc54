//
//  QuizManagementView.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> <PERSON><PERSON> on 24/7/25.
//

import SwiftUI

struct QuizManagementView: View {
    @StateObject private var viewModel = QuizManagementViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var showingCreateQuiz = false {
        didSet {
            print("🎯 DEBUG: showingCreateQuiz changed to: \(showingCreateQuiz)")
        }
    }
    @State private var showingQuizDetail: Quiz?

    init() {
        print("🎯 DEBUG: QuizManagementView init() called")
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 32) {
                    // Header Section
                    headerSection

                    // Stats Section
                    if !viewModel.quizzes.isEmpty {
                        statsSection
                    }

                    // Quick Actions Section
                    quickActionsSection

                    // Search and Filters Section
                    searchAndFiltersSection

                    // Content Section
                    contentSection

                    Spacer(minLength: 80)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationBarHidden(true)
            .background(
                Color.white
                    .ignoresSafeArea()
            )
            .refreshable {
                NSLog("📱 🔄 Pull-to-refresh triggered - refreshing data")
                await viewModel.refreshData()
            }
            .navigationDestination(isPresented: $showingCreateQuiz) {
                CreateQuizView()
                    .environmentObject(authViewModel)
            }
            .sheet(item: $showingQuizDetail) { quiz in
                QuizDetailView(quiz: quiz)
            }
            .sheet(isPresented: $viewModel.showingFilters) {
                QuizFiltersView(viewModel: viewModel)
            }
            .alert("Lỗi", isPresented: $viewModel.showingError) {
                Button("OK") {
                    viewModel.showingError = false
                }
            } message: {
                Text(viewModel.errorMessage ?? "Đã xảy ra lỗi không xác định")
            }
            .lazyLoad(viewModel: viewModel)
            .onAppear {
                NSLog("📱 🎬 QuizManagementView .onAppear triggered")
                NSLog("📱 🎬 Current quiz count: %d", viewModel.quizzes.count)
                NSLog("📱 🎬 Has initialized: %@", viewModel.hasInitialized ? "YES" : "NO")
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                NSLog("📱 🔄 App entering foreground - checking token and refreshing data")
                // Only refresh if we have data already (avoid double loading)
                if !viewModel.quizzes.isEmpty {
                    Task {
                        // Check if token is still valid
                        if TokenManager.shared.getToken() != nil {
                            NSLog("📱 ✅ Token exists - refreshing quiz data")
                            await viewModel.fetchLatestQuizzes()
                        } else {
                            NSLog("📱 ❌ No token found - user needs to login")
                            // Handle logout or redirect to login
                        }
                    }
                } else {
                    NSLog("📱 📱 No data yet - skipping foreground refresh")
                }
            }
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Center title section
            VStack(alignment: .center, spacing: 4) {
                Text("Quản lý Quiz")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Tạo và quản lý các bài kiểm tra")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Action button (similar to ProfileView Edit button)
            Button(action: {
                showingCreateQuiz = true
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "plus")
                        .font(.system(size: 14, weight: .medium))
                    Text("Tạo")
                        .font(.beVietnamPro(.medium, size: 14))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDark]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(20)
                .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
            }
        }
        .padding(.bottom, 8)
    }

    // MARK: - Stats Section
    private var statsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Thống kê")
                    .font(.beVietnamPro(.bold, size: 20))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()
            }

            HStack(spacing: 12) {
                ModernStatsCard(
                    title: "Tổng quiz",
                    value: "\(viewModel.totalCount)",
                    subtitle: viewModel.filteredQuizzesCount,
                    icon: "questionmark.circle.fill",
                    iconColor: AppConstants.Colors.primary,
                    iconBackground: AppConstants.Colors.primary.opacity(0.15)
                )

                ModernStatsCard(
                    title: "Cần chấm",
                    value: "\(viewModel.pendingGradingCount)",
                    subtitle: "bài thi",
                    icon: "clock.fill",
                    iconColor: AppConstants.Colors.warning,
                    iconBackground: AppConstants.Colors.warning.opacity(0.15)
                )
            }
        }
    }

    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Thao tác nhanh")
                    .font(.beVietnamPro(.bold, size: 20))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                Spacer()
            }

            VStack(spacing: 12) {
                ModernMenuRow(
                    icon: "plus.circle.fill",
                    title: "Tạo Quiz mới",
                    subtitle: "Tạo bài kiểm tra cho học sinh",
                    iconColor: AppConstants.Colors.primary,
                    iconBackground: AppConstants.Colors.primary.opacity(0.15)
                ) {
                    showingCreateQuiz = true
                }

                ModernMenuRow(
                    icon: "doc.text.fill",
                    title: "Mẫu Quiz",
                    subtitle: "Sử dụng mẫu có sẵn",
                    iconColor: AppConstants.Colors.secondary,
                    iconBackground: AppConstants.Colors.secondary.opacity(0.15)
                ) {
                    // TODO: Show quiz templates
                }

                ModernMenuRow(
                    icon: "chart.bar.fill",
                    title: "Báo cáo chi tiết",
                    subtitle: "Xem thống kê và phân tích",
                    iconColor: AppConstants.Colors.success,
                    iconBackground: AppConstants.Colors.success.opacity(0.15)
                ) {
                    // TODO: Show detailed reports
                }

                // 🔍 DEBUG: Temporary debug button
                #if DEBUG
                ModernMenuRow(
                    icon: "wrench.fill",
                    title: "🔍 Debug API",
                    subtitle: "Kiểm tra kết nối và token",
                    iconColor: .orange,
                    iconBackground: Color.orange.opacity(0.15)
                ) {
                    viewModel.debugAPIConnection()
                }
                #endif
            }
        }
    }

    // MARK: - Search and Filters Section
    private var searchAndFiltersSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Tìm kiếm & Lọc")
                    .font(.beVietnamPro(.bold, size: 20))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                Spacer()
            }

            QuizSearchAndFiltersView(viewModel: viewModel)
        }
    }

    // MARK: - Content Section
    private var contentSection: some View {
        VStack(spacing: 16) {
            if viewModel.isLoading && viewModel.quizzes.isEmpty {
                let _ = NSLog("📱 🎬 SHOWING: Loading view (isLoading: %@, quizzes.count: %d)", viewModel.isLoading ? "true" : "false", viewModel.quizzes.count)
                QuizLoadingView(message: "Đang tải danh sách quiz...")
                    .frame(maxWidth: .infinity, minHeight: 200)
            } else if viewModel.quizzes.isEmpty {
                let _ = NSLog("📱 🎬 SHOWING: Empty view (isLoading: %@, quizzes.count: %d)", viewModel.isLoading ? "true" : "false", viewModel.quizzes.count)
                EmptyQuizView {
                    showingCreateQuiz = true
                }
            } else {
                let _ = NSLog("📱 🎬 SHOWING: Quiz list (isLoading: %@, quizzes.count: %d)", viewModel.isLoading ? "true" : "false", viewModel.quizzes.count)
                VStack(spacing: 16) {
                    HStack {
                        Text("Danh sách Quiz")
                            .font(.beVietnamPro(.bold, size: 20))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        Spacer()
                    }

                    LazyVStack(spacing: 12) {
                        ForEach(viewModel.quizzes) { quiz in
                            QuizRowView(quiz: quiz, viewModel: viewModel)
                                .onTapGesture {
                                    showingQuizDetail = quiz
                                }
                        }

                        // Load more indicator
                        if viewModel.hasMorePages {
                            HStack {
                                Spacer()
                                if viewModel.isLoading {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Button("Tải thêm") {
                                        Task {
                                            await viewModel.loadMoreQuizzes()
                                        }
                                    }
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.primary)
                                }
                                Spacer()
                            }
                            .padding(.vertical, 8)
                            .onAppear {
                                Task {
                                    await viewModel.loadMoreQuizzes()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Modern Stats Card
struct ModernStatsCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let iconColor: Color
    let iconBackground: Color

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                ZStack {
                    RoundedRectangle(cornerRadius: 10)
                        .fill(iconBackground)
                        .frame(width: 36, height: 36)

                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(iconColor)
                }

                Spacer()
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text(title)
                    .font(.beVietnamPro(.semiBold, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Text(subtitle)
                    .font(.beVietnamPro(.medium, size: 12))
                    .foregroundColor(AppConstants.Colors.textTertiary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.04), radius: 8, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.black.opacity(0.06), lineWidth: 1)
        )
    }
}



// MARK: - Quiz Stats Header
struct QuizStatsHeaderView: View {
    @ObservedObject var viewModel: QuizManagementViewModel
    
    var body: some View {
        HStack(spacing: 12) {
            QuickStatsCard(
                title: "Tổng quiz",
                value: "\(viewModel.totalCount)",
                subtitle: viewModel.filteredQuizzesCount,
                icon: "questionmark.circle.fill",
                color: AppConstants.Colors.primary,
                trend: nil
            )
            
            QuickStatsCard(
                title: "Cần chấm",
                value: "\(viewModel.pendingGradingCount)",
                subtitle: "bài thi",
                icon: "clock.fill",
                color: AppConstants.Colors.warning,
                trend: nil
            )
        }
    }
}

// MARK: - Search and Filters
struct QuizSearchAndFiltersView: View {
    @ObservedObject var viewModel: QuizManagementViewModel

    var body: some View {
        VStack(spacing: 16) {
            // Search bar with modern styling
            HStack {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    TextField("Tìm kiếm quiz...", text: $viewModel.searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .onSubmit {
                            Task {
                                await viewModel.applyFilters()
                            }
                        }

                    if !viewModel.searchText.isEmpty {
                        Button {
                            viewModel.searchText = ""
                            Task {
                                await viewModel.applyFilters()
                            }
                        } label: {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.04), radius: 4, x: 0, y: 2)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.black.opacity(0.06), lineWidth: 1)
                )

                Button {
                    viewModel.showingFilters = true
                } label: {
                    HStack(spacing: 4) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                        if viewModel.hasActiveFilters {
                            Circle()
                                .fill(AppConstants.Colors.error)
                                .frame(width: 8, height: 8)
                        }
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color.white)
                    .cornerRadius(12)
                    .shadow(color: Color.black.opacity(0.04), radius: 4, x: 0, y: 2)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.black.opacity(0.06), lineWidth: 1)
                    )
                }
            }
            
            // Active filters
            if viewModel.hasActiveFilters {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        if let quizType = viewModel.selectedQuizType {
                            QuizFilterChip(
                                title: quizType.displayName,
                                onRemove: {
                                    viewModel.selectedQuizType = nil
                                    Task {
                                        await viewModel.applyFilters()
                                    }
                                }
                            )
                        }

                        if let state = viewModel.selectedState {
                            QuizFilterChip(
                                title: state.displayName,
                                onRemove: {
                                    viewModel.selectedState = nil
                                    Task {
                                        await viewModel.applyFilters()
                                    }
                                }
                            )
                        }
                        
                        Button("Xóa tất cả") {
                            Task {
                                await viewModel.clearFilters()
                            }
                        }
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                    }
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                }
            }
        }
    }
}

// MARK: - Quiz Filter Chip
struct QuizFilterChip: View {
    let title: String
    let onRemove: () -> Void
    
    var body: some View {
        HStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.caption2)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(AppConstants.Colors.border, lineWidth: 1)
        )
    }
}

// MARK: - Quiz List
struct QuizListView: View {
    @ObservedObject var viewModel: QuizManagementViewModel
    let onQuizTap: (Quiz) -> Void
    
    var body: some View {
        List {
            ForEach(viewModel.quizzes) { quiz in
                QuizRowView(quiz: quiz, viewModel: viewModel)
                    .onTapGesture {
                        onQuizTap(quiz)
                    }
                    .listRowInsets(EdgeInsets(
                        top: 8,
                        leading: AppConstants.UI.screenPadding,
                        bottom: 8,
                        trailing: AppConstants.UI.screenPadding
                    ))
                    .listRowSeparator(.hidden)
                    .listRowBackground(Color.clear)
            }
            
            // Load more indicator
            if viewModel.hasMorePages {
                HStack {
                    Spacer()
                    if viewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Button("Tải thêm") {
                            Task {
                                await viewModel.loadMoreQuizzes()
                            }
                        }
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                    }
                    Spacer()
                }
                .padding(.vertical, 8)
                .listRowInsets(EdgeInsets())
                .listRowSeparator(.hidden)
                .listRowBackground(Color.clear)
                .onAppear {
                    Task {
                        await viewModel.loadMoreQuizzes()
                    }
                }
            }
        }
        .listStyle(PlainListStyle())
    }
}

// MARK: - Empty Quiz View
struct EmptyQuizView: View {
    let onCreateQuiz: () -> Void

    var body: some View {
        VStack(spacing: 32) {
            // Modern empty state illustration
            ZStack {
                RoundedRectangle(cornerRadius: 24)
                    .fill(AppConstants.Colors.primary.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: "questionmark.circle.fill")
                    .font(.system(size: 48))
                    .foregroundColor(AppConstants.Colors.primary)
            }

            VStack(spacing: 12) {
                Text("Chưa có quiz nào")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Tạo quiz đầu tiên để bắt đầu đánh giá học sinh của bạn")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
            }

            VStack(spacing: 16) {
                Button {
                    onCreateQuiz()
                } label: {
                    HStack(spacing: 8) {
                        Image(systemName: "plus")
                        Text("Tạo Quiz đầu tiên")
                    }
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(AppConstants.Colors.primary)
                    .cornerRadius(12)
                    .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
                }

                Button {
                    // TODO: Show quiz templates
                } label: {
                    HStack(spacing: 8) {
                        Image(systemName: "doc.text")
                        Text("Xem mẫu Quiz")
                    }
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(Color.white)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(AppConstants.Colors.primary, lineWidth: 2)
                    )
                }
            }
        }
        .padding(32)
        .background(Color.white)
        .cornerRadius(20)
        .shadow(color: Color.black.opacity(0.04), radius: 12, x: 0, y: 8)
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.black.opacity(0.06), lineWidth: 1)
        )
        .padding(.horizontal, 20)
    }
}

// MARK: - Quiz Loading View
struct QuizLoadingView: View {
    let message: String
    
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text(message)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
}

// MARK: - Preview
struct QuizManagementView_Previews: PreviewProvider {
    static var previews: some View {
        QuizManagementView()
    }
}
