//
//  LessonsAPIDemo.swift
//  mobile-app-template
//
//  Created by Inst<PERSON><PERSON> App on 28/7/25.
//

import SwiftUI

// MARK: - Simple Lesson Model
struct DemoLesson: Codable, Identifiable {
    let id: Int
    let name: String
    let className: String
    let classCode: String
    let startDatetime: String
    let room: String?
    let totalStudents: Int
    let isToday: Bool
    let isUpcoming: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, room
        case className = "class_name"
        case classCode = "class_code"
        case startDatetime = "start_datetime"
        case totalStudents = "total_students"
        case isToday = "is_today"
        case isUpcoming = "is_upcoming"
    }
    
    var timeString: String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: startDatetime) {
            let timeFormatter = DateFormatter()
            timeFormatter.timeStyle = .short
            return timeFormatter.string(from: date)
        }
        return "N/A"
    }
    
    var statusText: String {
        if isToday { return "Hôm nay" }
        if isUpcoming { return "Sắp tới" }
        return "Đã qua"
    }
    
    var statusColor: Color {
        if isToday { return .blue }
        if isUpcoming { return .green }
        return .gray
    }
}

// MARK: - API Response
struct DemoLessonsResponse: Codable {
    let success: Bool
    let message: String
    let data: [DemoLesson]
}

// MARK: - Lessons API Demo View
struct LessonsAPIDemo: View {
    @StateObject private var viewModel = LessonsAPIDemoViewModel()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 0) {
                    // Header
                    headerSection

                    // Content
                    if viewModel.isLoading {
                        loadingView
                    } else if let errorMessage = viewModel.errorMessage {
                        errorView(errorMessage)
                    } else {
                        lessonsList
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationBarHidden(true)
            .background(
                Color.white
                    .ignoresSafeArea()
            )
            .task {
                await viewModel.loadLessons()
            }
        }
    }
    
    // MARK: - Header
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Title - centered
            VStack(alignment: .center, spacing: 4) {
                Text("Lớp học")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Quản lý lịch học và lớp học")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity)

            // Stats
            if !viewModel.lessons.isEmpty {
                HStack(spacing: 20) {
                    StatView(title: "Tổng số", value: "\(viewModel.lessons.count)", color: .blue)
                    StatView(title: "Hôm nay", value: "\(viewModel.todayCount)", color: .green)
                    StatView(title: "Sắp tới", value: "\(viewModel.upcomingCount)", color: .orange)
                }
            }
        }
        .padding(.bottom, 24)
    }
    
    // MARK: - Lessons List
    private var lessonsList: some View {
        LazyVStack(spacing: 12) {
            ForEach(viewModel.lessons) { lesson in
                LessonCardView(lesson: lesson)
            }
        }
        .padding(.top, 8)
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)

            Text("Đang tải từ API...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 50)
    }

    // MARK: - Error View
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)

            Text("Lỗi API")
                .font(.headline)

            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            Button("Thử lại") {
                Task {
                    await viewModel.loadLessons()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 50)
    }
}

// MARK: - Lesson Card View
struct LessonCardView: View {
    let lesson: DemoLesson
    @State private var showingLessonDetail = false

    var body: some View {
        Button(action: {
            showingLessonDetail = true
        }) {
            HStack(alignment: .top, spacing: 16) {
                // Thumbnail icon
                ZStack {
                    // Outer light blue circle
                    Circle()
                        .fill(Color(red: 0.9, green: 0.95, blue: 1.0))
                        .frame(width: 56, height: 56)

                    // Inner blue circle
                    Circle()
                        .fill(Color(red: 0.4, green: 0.7, blue: 1.0))
                        .frame(width: 40, height: 40)

                    Image(systemName: "book.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                }

                // Content
                VStack(alignment: .leading, spacing: 12) {
                    // Header
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(lesson.classCode)
                                .font(.beVietnamPro(.medium, size: 12))
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Text(lesson.name)
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                                .lineLimit(2)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Status badge
                        Text(lesson.statusText)
                            .font(.beVietnamPro(.medium, size: 11))
                            .foregroundColor(lesson.statusColor)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(lesson.statusColor.opacity(0.1))
                            .cornerRadius(12)
                    }

                    // Info row
                    HStack(spacing: 16) {
                        // Time
                        HStack(spacing: 6) {
                            Image(systemName: "clock")
                                .font(.system(size: 12))
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Text(lesson.timeString)
                                .font(.beVietnamPro(.medium, size: 12))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }

                        // Location
                        if let room = lesson.room {
                            HStack(spacing: 6) {
                                Image(systemName: "location")
                                    .font(.system(size: 12))
                                    .foregroundColor(AppConstants.Colors.textSecondary)

                                Text(room)
                                    .font(.beVietnamPro(.medium, size: 12))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            }
                        }

                        Spacer()

                        // Students count
                        HStack(spacing: 4) {
                            Image(systemName: "person.2")
                                .font(.system(size: 12))
                                .foregroundColor(.blue)

                            Text("\(lesson.totalStudents)")
                                .font(.beVietnamPro(.medium, size: 12))
                                .foregroundColor(.blue)
                        }
                    }

                    // Class name
                    Text(lesson.className)
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(1)
                }
            }
            .padding(16)
        }
        .buttonStyle(PlainButtonStyle())
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.black.opacity(0.1), lineWidth: 0.5)
        )
        .sheet(isPresented: $showingLessonDetail) {
            DemoLessonDetailBottomSheet(lesson: lesson)
        }
    }
}

// MARK: - Stat View
struct StatView: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - View Model
@MainActor
class LessonsAPIDemoViewModel: ObservableObject {
    @Published var lessons: [DemoLesson] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let baseURL = "https://lms-dev.ebill.vn/api/v1"
    
    func loadLessons() async {
        isLoading = true
        errorMessage = nil
        
        do {
            print("🚀 Starting API call...")
            
            // Step 1: Login
            let token = try await login()
            
            // Step 2: Fetch lessons
            let response = try await fetchLessons(token: token)
            
            lessons = response.data
            print("✅ Loaded \(lessons.count) lessons successfully")
            
        } catch {
            errorMessage = "API Error: \(error.localizedDescription)"
            print("❌ API Error: \(error)")
        }
        
        isLoading = false
    }
    
    private func login() async throws -> String {
        print("🔐 Logging in...")
        
        guard let url = URL(string: "\(baseURL)/auth/sign-in") else {
            throw URLError(.badURL)
        }
        
        let loginData = [
            "username": "<EMAIL>",
            "password": "earnbase@X2025",
            "remember_me": false
        ] as [String : Any]
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: loginData)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw URLError(.badServerResponse)
        }
        
        if httpResponse.statusCode == 200 {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let success = json["success"] as? Bool,
               success,
               let data = json["data"] as? [String: Any],
               let accessToken = data["access_token"] as? String {
                
                print("✅ Login successful")
                return accessToken
            }
        }
        
        throw URLError(.userAuthenticationRequired)
    }
    
    private func fetchLessons(token: String) async throws -> DemoLessonsResponse {
        print("📚 Fetching lessons...")
        
        guard let url = URL(string: "\(baseURL)/instructors/lessons?page=1&page_size=20") else {
            throw URLError(.badURL)
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw URLError(.badServerResponse)
        }
        
        if httpResponse.statusCode == 200 {
            let lessonResponse = try JSONDecoder().decode(DemoLessonsResponse.self, from: data)
            print("✅ Lessons fetched successfully")
            return lessonResponse
        } else {
            print("❌ Lessons API failed with status: \(httpResponse.statusCode)")
            throw URLError(.badServerResponse)
        }
    }
    
    var todayCount: Int {
        lessons.filter { $0.isToday }.count
    }
    
    var upcomingCount: Int {
        lessons.filter { $0.isUpcoming }.count
    }
}

#Preview {
    LessonsAPIDemo()
}
