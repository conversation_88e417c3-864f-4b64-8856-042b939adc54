//
//  QuizRepository.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> A<PERSON> on 24/7/25.
//

import Foundation

// MARK: - Codable Extension
extension Encodable {
    func asDictionary() throws -> [String: Any] {
        let data = try JSONEncoder().encode(self)
        guard let dictionary = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String: Any] else {
            throw NSError(domain: "EncodingError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to convert to dictionary"])
        }
        return dictionary
    }
}

// MARK: - Debug Logging Helper
private func writeLogToFile(_ message: String) {
    let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    let logFile = documentsPath.appendingPathComponent("quiz_debug.log")

    let formatter = DateFormatter()
    formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
    let timestamp = formatter.string(from: Date())
    let logEntry = "\(timestamp): \(message)\n"

    if let data = logEntry.data(using: .utf8) {
        if FileManager.default.fileExists(atPath: logFile.path) {
            if let fileHandle = try? FileHandle(forWritingTo: logFile) {
                fileHandle.seekToEndOfFile()
                fileHandle.write(data)
                fileHandle.closeFile()
            }
        } else {
            try? data.write(to: logFile)
        }
    }
}

// MARK: - Quiz Repository Protocol
protocol QuizRepositoryProtocol {
    func getQuizzes(page: Int, limit: Int, filters: QuizFilters?) async throws -> QuizListResponse
    func createQuiz(_ request: CreateQuizRequest) async throws -> Quiz
    func markQuizReady(quizId: Int) async throws -> QuizReadyResponse
    func assignQuizToLesson(lessonId: Int, request: AssignQuizRequest) async throws -> LessonQuizResponse
    func publishQuiz(lessonId: Int, quizId: Int) async throws -> PublishQuizResponse
    func unpublishQuiz(lessonId: Int, quizId: Int) async throws -> UnpublishQuizResponse
    func archiveQuiz(quizId: Int) async throws -> QuizStateResponse
    func deleteQuiz(quizId: Int) async throws -> QuizDeleteResponse
    func getQuizAnalytics(quizId: Int) async throws -> QuizAnalytics
}

// MARK: - Quiz Repository Implementation
class QuizRepository: QuizRepositoryProtocol {
    private let apiClient = APIClient.shared
    
    func getQuizzes(page: Int = 1, limit: Int = 20, filters: QuizFilters? = nil) async throws -> QuizListResponse {
        var queryParams: [String: String] = [
            "page": "\(page)",
            "limit": "\(limit)"
        ]
        
        // Add filters if provided
        if let filters = filters {
            if let quizType = filters.quizType {
                queryParams["quiz_type"] = quizType.rawValue
            }
            if let state = filters.state {
                queryParams["state"] = state.rawValue
            }
            if let subjectId = filters.subjectId {
                queryParams["subject_id"] = "\(subjectId)"
            }
            if let classId = filters.classId {
                queryParams["class_id"] = "\(classId)"
            }
            if let search = filters.search, !search.isEmpty {
                queryParams["search"] = search
            }
        }
        
        let queryString = queryParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        let endpoint = "instructors/quizzes?\(queryString)"

        NSLog("🎯 ===== QUIZ REPOSITORY REQUEST =====")
        NSLog("🎯 Full URL: %@", "\(APIConfiguration.shared.baseURL)\(endpoint)")
        NSLog("🎯 Endpoint: %@", endpoint)
        NSLog("🎯 Query Params: %@", "\(queryParams)")
        let token = await MainActor.run { TokenManager.shared.getToken() }
        NSLog("🎯 Current Token: %@", token ?? "NO TOKEN")
        NSLog("🎯 Timestamp: %@", "\(Date())")
        NSLog("🎯 =====================================")

        do {
            let response = try await apiClient.request(
                endpoint: endpoint,
                method: .GET,
                responseType: QuizListResponse.self,
                requiresAuth: true
            )
            print("🎯 ===== QUIZ REPOSITORY SUCCESS =====")
            print("🎯 API call successful!")
            print("🎯 Quiz count: \(response.data.quizzes.count)")
            print("🎯 Total count: \(response.data.totalCount)")
            print("🎯 Current page: \(response.data.currentPage)")
            print("🎯 Total pages: \(response.data.totalPages)")
            print("🎯 Success: \(response.success)")
            print("🎯 Message: \(response.message)")

            // Debug individual quiz data
            print("🎯 📋 QUIZ LIST DEBUG:")
            for (index, quiz) in response.data.quizzes.enumerated() {
                print("🎯 Quiz \(index + 1): \(quiz.name) (ID: \(quiz.id), State: \(quiz.state.rawValue))")
            }
            print("🎯 ====================================")
            return response
        } catch {
            print("🎯 ===== QUIZ REPOSITORY ERROR =====")
            print("🎯 API call failed!")
            print("🎯 Error: \(error)")
            print("🎯 Error type: \(type(of: error))")
            print("🎯 Error description: \(error.localizedDescription)")

            // 🔍 ENHANCED ERROR DEBUGGING
            NSLog("🚨 ===== DETAILED ERROR ANALYSIS =====")
            NSLog("🚨 Error: %@", "\(error)")
            NSLog("🚨 Error type: %@", "\(type(of: error))")
            NSLog("🚨 Error localized: %@", error.localizedDescription)

            if let nsError = error as NSError? {
                NSLog("🚨 NSError domain: %@", nsError.domain)
                NSLog("🚨 NSError code: %d", nsError.code)
                NSLog("🚨 NSError userInfo: %@", "\(nsError.userInfo)")
            }

            if let urlError = error as? URLError {
                NSLog("🚨 URLError code: %@", "\(urlError.code)")
                NSLog("🚨 URLError description: %@", urlError.localizedDescription)
            }

            // Check token status
            let hasToken = await MainActor.run { TokenManager.shared.getToken() != nil }
            NSLog("🚨 Has auth token: %@", hasToken ? "YES" : "NO")
            NSLog("🚨 =====================================")

            // 🚨 Re-throw the error to let ViewModel handle it properly
            print("🎯 ❌ Re-throwing error to ViewModel for proper handling")
            NSLog("🎯 ❌ Re-throwing error to ViewModel for proper handling")
            throw error

            // If it's a decoding error and the API might be returning empty or different format,
            // we can return an empty response instead of throwing
            if error is DecodingError {
                print("🎯 Decoding error detected - possibly empty response or different format")
                print("🎯 Returning empty quiz list instead of throwing error")

                // Return empty response structure
                return QuizListResponse(
                    success: true,
                    message: "No quizzes found",
                    data: QuizListData(
                        quizzes: [],
                        totalCount: 0,
                        page: 1,
                        limit: 20
                    ),
                    meta: QuizListMeta(
                        timestamp: "\(Date())",
                        traceId: "empty-response",
                        requestId: "empty-response"
                    )
                )
            }

            if let networkError = error as? NetworkError {
                print("🎯 Network error details: \(networkError)")
                print("🎯 Network error description: \(networkError.localizedDescription)")

                // For development: fallback to mock data if network fails
                // TEMPORARILY DISABLED TO TEST REAL API
                /*
                #if DEBUG
                print("🎯 🔄 Network failed - falling back to mock data for development")
                let mockRepo = MockQuizRepository()
                return try await mockRepo.getQuizzes(page: page, limit: limit, filters: filters)
                #endif
                */
            }
            print("🎯 ==================================")
            throw error
        }
    }
    
    func createQuiz(_ request: CreateQuizRequest) async throws -> Quiz {
        let response: CreateQuizResponse = try await apiClient.request(
            endpoint: "instructors/quizzes",
            method: .POST,
            parameters: try request.asDictionary(),
            responseType: CreateQuizResponse.self
        )
        return response.data
    }
    
    func markQuizReady(quizId: Int) async throws -> QuizReadyResponse {
        return try await apiClient.request(
            endpoint: "instructors/quizzes/\(quizId)/ready",
            method: .POST,
            responseType: QuizReadyResponse.self
        )
    }
    
    func assignQuizToLesson(lessonId: Int, request: AssignQuizRequest) async throws -> LessonQuizResponse {
        return try await apiClient.request(
            endpoint: "instructors/lessons/\(lessonId)/quizzes",
            method: .POST,
            parameters: try request.asDictionary(),
            responseType: LessonQuizResponse.self
        )
    }
    
    func publishQuiz(lessonId: Int, quizId: Int) async throws -> PublishQuizResponse {
        return try await apiClient.request(
            endpoint: "instructors/lessons/\(lessonId)/quizzes/\(quizId)/publish",
            method: .POST,
            responseType: PublishQuizResponse.self
        )
    }
    
    func unpublishQuiz(lessonId: Int, quizId: Int) async throws -> UnpublishQuizResponse {
        return try await apiClient.request(
            endpoint: "instructors/lessons/\(lessonId)/quizzes/\(quizId)/unpublish",
            method: .POST,
            responseType: UnpublishQuizResponse.self
        )
    }
    
    func getQuizAnalytics(quizId: Int) async throws -> QuizAnalytics {
        let response: QuizAnalyticsResponse = try await apiClient.request(
            endpoint: "instructors/quizzes/\(quizId)/analytics",
            method: .GET,
            responseType: QuizAnalyticsResponse.self
        )
        return response.data
    }

    func archiveQuiz(quizId: Int) async throws -> QuizStateResponse {
        return try await apiClient.request(
            endpoint: "instructors/quizzes/\(quizId)/archive",
            method: .POST,
            responseType: QuizStateResponse.self
        )
    }

    func deleteQuiz(quizId: Int) async throws -> QuizDeleteResponse {
        return try await apiClient.request(
            endpoint: "instructors/quizzes/\(quizId)",
            method: .DELETE,
            responseType: QuizDeleteResponse.self
        )
    }
}

// MARK: - Quiz Analytics Response
struct QuizAnalyticsResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizAnalytics
    let traceId: String
    let requestId: String
}

// MARK: - Mock Quiz Repository for Testing
class MockQuizRepository: QuizRepositoryProtocol {
    private var quizzes: [Quiz] = []
    private var nextId = 1
    
    init() {
        // Add some mock data
        quizzes = [
            Quiz(
                id: 1,
                name: "Kiểm tra Toán học",
                code: "QUIZ001",
                description: "Kiểm tra chương 1: Đại số",
                quizType: .quiz,
                subjectId: 1,
                subjectName: "Toán học",
                classId: 1,
                className: "Lớp 10A1",
                maxScore: 100,
                passingScore: 70,
                timeLimit: 60,
                maxAttempts: 2,
                isRandomized: true,
                showCorrectAnswers: false,
                state: .published,
                questionCount: 10,
                studentCount: 30,
                attemptCount: 25,
                averageScore: 78.5,
                passRate: 80.0,
                pendingGradingCount: 3,
                startDate: Date().addingTimeInterval(-86400), // 1 day ago
                endDate: Date().addingTimeInterval(86400), // 1 day from now
                createdAt: Date().addingTimeInterval(-172800), // 2 days ago
                updatedAt: Date().addingTimeInterval(-86400)
            ),
            Quiz(
                id: 2,
                name: "Bài tập Văn học",
                code: "QUIZ002",
                description: "Phân tích tác phẩm Truyện Kiều",
                quizType: .assignment,
                subjectId: 2,
                subjectName: "Văn học",
                classId: 1,
                className: "Lớp 10A1",
                maxScore: 50,
                passingScore: 30,
                timeLimit: nil,
                maxAttempts: 1,
                isRandomized: false,
                showCorrectAnswers: true,
                state: .ready,
                questionCount: 5,
                studentCount: 30,
                attemptCount: 0,
                averageScore: 0,
                passRate: 0,
                pendingGradingCount: 0,
                startDate: nil,
                endDate: nil,
                createdAt: Date().addingTimeInterval(-86400),
                updatedAt: nil
            )
        ]
        nextId = 3
    }
    
    func getQuizzes(page: Int, limit: Int, filters: QuizFilters?) async throws -> QuizListResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        var filteredQuizzes = quizzes
        
        // Apply filters
        if let filters = filters {
            if let quizType = filters.quizType {
                filteredQuizzes = filteredQuizzes.filter { $0.quizType == quizType }
            }
            if let state = filters.state {
                filteredQuizzes = filteredQuizzes.filter { $0.state == state }
            }
            if let search = filters.search, !search.isEmpty {
                filteredQuizzes = filteredQuizzes.filter { 
                    $0.name.localizedCaseInsensitiveContains(search) ||
                    $0.code.localizedCaseInsensitiveContains(search)
                }
            }
        }
        
        // Apply pagination
        let startIndex = (page - 1) * limit
        let endIndex = min(startIndex + limit, filteredQuizzes.count)
        let paginatedQuizzes = Array(filteredQuizzes[startIndex..<endIndex])
        
        let totalPages = Int(ceil(Double(filteredQuizzes.count) / Double(limit)))
        
        return QuizListResponse(
            success: true,
            message: "Quizzes retrieved successfully",
            data: QuizListData(
                quizzes: paginatedQuizzes,
                totalCount: filteredQuizzes.count,
                page: page,
                limit: limit
            ),
            meta: QuizListMeta(
                timestamp: "\(Date())",
                traceId: UUID().uuidString,
                requestId: UUID().uuidString
            )
        )
    }
    
    func createQuiz(_ request: CreateQuizRequest) async throws -> Quiz {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        let newQuiz = Quiz(
            id: nextId,
            name: request.name,
            code: "QUIZ\(String(format: "%03d", nextId))",
            description: request.description,
            quizType: request.quizType,
            subjectId: request.subjectId,
            subjectName: nil,
            classId: request.classId,
            className: nil,
            maxScore: request.maxScore,
            passingScore: request.passingScore,
            timeLimit: request.timeLimit,
            maxAttempts: request.maxAttempts,
            isRandomized: request.isRandomized,
            showCorrectAnswers: request.showCorrectAnswers,
            state: .draft,
            questionCount: request.questions.count,
            studentCount: 0,
            attemptCount: 0,
            averageScore: 0,
            passRate: 0,
            pendingGradingCount: 0,
            startDate: nil,
            endDate: nil,
            createdAt: Date(),
            updatedAt: nil
        )
        
        quizzes.insert(newQuiz, at: 0)
        nextId += 1
        
        return newQuiz
    }
    
    func markQuizReady(quizId: Int) async throws -> QuizReadyResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        guard let index = quizzes.firstIndex(where: { $0.id == quizId }) else {
            throw APIError(code: "NOT_FOUND", message: "Quiz not found", field: nil, details: nil)
        }
        
        var quiz = quizzes[index]
        quiz = Quiz(
            id: quiz.id,
            name: quiz.name,
            code: quiz.code,
            description: quiz.description,
            quizType: quiz.quizType,
            subjectId: quiz.subjectId,
            subjectName: quiz.subjectName,
            classId: quiz.classId,
            className: quiz.className,
            maxScore: quiz.maxScore,
            passingScore: quiz.passingScore,
            timeLimit: quiz.timeLimit,
            maxAttempts: quiz.maxAttempts,
            isRandomized: quiz.isRandomized,
            showCorrectAnswers: quiz.showCorrectAnswers,
            state: .ready,
            questionCount: quiz.questionCount,
            studentCount: quiz.studentCount,
            attemptCount: quiz.attemptCount,
            averageScore: quiz.averageScore,
            passRate: quiz.passRate,
            pendingGradingCount: quiz.pendingGradingCount,
            startDate: quiz.startDate,
            endDate: quiz.endDate,
            createdAt: quiz.createdAt,
            updatedAt: Date()
        )
        
        quizzes[index] = quiz
        
        return QuizReadyResponse(
            success: true,
            message: "Quiz marked as ready successfully",
            data: QuizReadyData(
                quizId: quizId,
                state: .ready,
                message: "Quiz is now ready for assignment"
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
    
    func assignQuizToLesson(lessonId: Int, request: AssignQuizRequest) async throws -> LessonQuizResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        return LessonQuizResponse(
            success: true,
            message: "Quiz assigned to lesson successfully",
            data: LessonQuizData(
                lessonId: lessonId,
                quizId: request.quizId,
                startDate: request.startDate,
                endDate: request.endDate,
                instructions: request.instructions,
                state: "assigned"
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
    
    func publishQuiz(lessonId: Int, quizId: Int) async throws -> PublishQuizResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        return PublishQuizResponse(
            success: true,
            message: "Quiz published successfully",
            data: PublishQuizData(
                lessonId: lessonId,
                quizId: quizId,
                publishedAt: Date(),
                studentsNotified: 30
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
    
    func unpublishQuiz(lessonId: Int, quizId: Int) async throws -> UnpublishQuizResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        return UnpublishQuizResponse(
            success: true,
            message: "Quiz unpublished successfully",
            data: UnpublishQuizData(
                lessonId: lessonId,
                quizId: quizId,
                unpublishedAt: Date()
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
    
    func getQuizAnalytics(quizId: Int) async throws -> QuizAnalytics {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        // Find the quiz to get its name
        let quiz = quizzes.first { $0.id == quizId }
        let quizName = quiz?.name ?? "Quiz không tìm thấy"

        // Generate mock question analytics
        let questionAnalytics = [
            QuestionAnalytics(
                id: 1,
                questionId: 1,
                questionText: "Tính đạo hàm của hàm số f(x) = x² + 2x + 1",
                questionType: .singleChoice,
                totalAnswers: 25,
                correctAnswers: 20,
                averageScore: 8.0,
                difficulty: .medium
            ),
            QuestionAnalytics(
                id: 2,
                questionId: 2,
                questionText: "Giải phương trình bậc hai: x² - 5x + 6 = 0",
                questionType: .essay,
                totalAnswers: 25,
                correctAnswers: 15,
                averageScore: 6.5,
                difficulty: .hard
            ),
            QuestionAnalytics(
                id: 3,
                questionId: 3,
                questionText: "Số π có giá trị xấp xỉ bằng 3.14159",
                questionType: .trueFalse,
                totalAnswers: 25,
                correctAnswers: 23,
                averageScore: 9.2,
                difficulty: .easy
            )
        ]

        // Generate mock score distribution
        let scoreDistribution = [
            ScoreRange(id: "0-20", range: "0-20%", count: 2, percentage: 8.0),
            ScoreRange(id: "21-40", range: "21-40%", count: 3, percentage: 12.0),
            ScoreRange(id: "41-60", range: "41-60%", count: 5, percentage: 20.0),
            ScoreRange(id: "61-80", range: "61-80%", count: 8, percentage: 32.0),
            ScoreRange(id: "81-100", range: "81-100%", count: 7, percentage: 28.0)
        ]

        // Generate mock attempts by date (last 7 days)
        let calendar = Calendar.current
        let today = Date()
        let attemptsByDate = (0..<7).compactMap { dayOffset in
            let date = calendar.date(byAdding: .day, value: -dayOffset, to: today)!
            let count = Int.random(in: 0...5)
            return AttemptsByDate(
                id: "day-\(dayOffset)",
                date: date,
                count: count
            )
        }.reversed()

        return QuizAnalytics(
            id: 1,
            quizId: quizId,
            quizName: quizName,
            totalStudents: quiz?.studentCount ?? 30,
            totalAttempts: quiz?.attemptCount ?? 25,
            completedAttempts: quiz?.attemptCount ?? 22,
            averageScore: quiz?.averageScore ?? 78.5,
            highestScore: 95.0,
            lowestScore: 45.0,
            passRate: quiz?.passRate ?? 80.0,
            averageTimeSpent: 2400, // 40 minutes
            questionAnalytics: questionAnalytics,
            scoreDistribution: scoreDistribution,
            attemptsByDate: Array(attemptsByDate)
        )
    }

    func archiveQuiz(quizId: Int) async throws -> QuizStateResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        // Update quiz state in mock data
        if let index = quizzes.firstIndex(where: { $0.id == quizId }) {
            let quiz = quizzes[index]
            let updatedQuiz = Quiz(
                id: quiz.id,
                name: quiz.name,
                code: quiz.code,
                description: quiz.description,
                quizType: quiz.quizType,
                subjectId: quiz.subjectId,
                subjectName: quiz.subjectName,
                classId: quiz.classId,
                className: quiz.className,
                maxScore: quiz.maxScore,
                passingScore: quiz.passingScore,
                timeLimit: quiz.timeLimit,
                maxAttempts: quiz.maxAttempts,
                isRandomized: quiz.isRandomized,
                showCorrectAnswers: quiz.showCorrectAnswers,
                state: .archived,
                questionCount: quiz.questionCount,
                studentCount: quiz.studentCount,
                attemptCount: quiz.attemptCount,
                averageScore: quiz.averageScore,
                passRate: quiz.passRate,
                pendingGradingCount: quiz.pendingGradingCount,
                startDate: quiz.startDate,
                endDate: quiz.endDate,
                createdAt: quiz.createdAt,
                updatedAt: Date()
            )
            quizzes[index] = updatedQuiz
        }

        return QuizStateResponse(
            success: true,
            message: "Quiz archived successfully",
            data: QuizStateData(
                quizId: quizId,
                state: .archived,
                updatedAt: Date(),
                message: "Quiz has been archived"
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }

    func deleteQuiz(quizId: Int) async throws -> QuizDeleteResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        // Remove quiz from mock data
        quizzes.removeAll { $0.id == quizId }

        return QuizDeleteResponse(
            success: true,
            message: "Quiz deleted successfully",
            data: QuizDeleteData(
                quizId: quizId,
                deletedAt: Date(),
                message: "Quiz has been permanently deleted"
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
}
