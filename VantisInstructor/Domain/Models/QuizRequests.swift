//
//  QuizRequests.swift
//  mobile-app-template
//
//  Created by Instructor App on 24/7/25.
//

import Foundation

// MARK: - Quiz List Request/Response
struct QuizFilters: Codable {
    let quizType: QuizType?
    let state: QuizState?
    let subjectId: Int?
    let classId: Int?
    let search: String?
    
    init(quizType: QuizType? = nil, state: QuizState? = nil, subjectId: Int? = nil, classId: Int? = nil, search: String? = nil) {
        self.quizType = quizType
        self.state = state
        self.subjectId = subjectId
        self.classId = classId
        self.search = search
    }
}

struct QuizListResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizListData
    let meta: QuizListMeta?

    // Computed properties for compatibility
    var traceId: String { meta?.traceId ?? "" }
    var requestId: String { meta?.requestId ?? "" }
}

struct QuizListMeta: Codable {
    let timestamp: String
    let traceId: String?
    let requestId: String?

    enum CodingKeys: String, CodingKey {
        case timestamp
        case traceId = "trace_id"
        case requestId = "request_id"
    }
}

struct QuizListData: Codable {
    let quizzes: [Quiz]
    let totalCount: Int
    let page: Int
    let limit: Int

    // Computed properties for compatibility
    var currentPage: Int { page }
    var totalPages: Int {
        guard limit > 0 else { return 0 }
        return Int(ceil(Double(totalCount) / Double(limit)))
    }
    var hasNextPage: Bool { page < totalPages }
    var hasPreviousPage: Bool { page > 1 }

    enum CodingKeys: String, CodingKey {
        case quizzes
        case totalCount = "total_count"
        case page
        case limit
    }
}

// MARK: - Create Quiz Request/Response
struct CreateQuizRequest: Codable {
    let name: String
    let description: String?
    let instruction: String?
    let quizType: QuizType
    let subjectId: Int?
    let classId: Int?
    let maxScore: Double
    let passingScore: Double
    let timeLimit: Int?
    let maxAttempts: Int
    let startDate: Date?
    let endDate: Date?
    let isRandomized: Bool
    let showCorrectAnswers: Bool
    let showResultImmediately: Bool
    let questions: [CreateQuestionRequest]

    enum CodingKeys: String, CodingKey {
        case name, description, instruction
        case quizType = "quiz_type"
        case subjectId = "subject_id"
        case classId = "class_id"
        case maxScore = "max_score"
        case passingScore = "passing_score"
        case timeLimit = "time_limit"
        case maxAttempts = "max_attempts"
        case startDate = "start_date"
        case endDate = "end_date"
        case isRandomized = "is_randomized"
        case showCorrectAnswers = "show_correct_answers"
        case showResultImmediately = "show_result_immediately"
        case questions
    }
}

struct CreateQuestionRequest: Codable {
    let text: String
    let questionType: QuestionType
    let score: Double
    let explanation: String?
    let answers: [CreateAnswerRequest]

    enum CodingKeys: String, CodingKey {
        case text
        case questionType = "question_type"
        case score, explanation, answers
    }
}

struct CreateAnswerRequest: Codable {
    let text: String
    let isCorrect: Bool
    let explanation: String?

    enum CodingKeys: String, CodingKey {
        case text
        case isCorrect = "is_correct"
        case explanation
    }
}

struct CreateQuizResponse: Codable {
    let success: Bool
    let message: String
    let data: Quiz
    let traceId: String
    let requestId: String
}

// MARK: - Quiz Ready Request/Response
struct QuizReadyResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizReadyData
    let traceId: String
    let requestId: String
}

struct QuizReadyData: Codable {
    let quizId: Int
    let state: QuizState
    let message: String
    
    enum CodingKeys: String, CodingKey {
        case quizId = "quiz_id"
        case state, message
    }
}

// MARK: - Assign Quiz Request/Response
struct AssignQuizRequest: Codable {
    let quizId: Int
    let startDate: Date?
    let endDate: Date?
    let instructions: String?
    
    enum CodingKeys: String, CodingKey {
        case quizId = "quiz_id"
        case startDate = "start_date"
        case endDate = "end_date"
        case instructions
    }
}

struct LessonQuizResponse: Codable {
    let success: Bool
    let message: String
    let data: LessonQuizData
    let traceId: String
    let requestId: String
}

struct LessonQuizData: Codable {
    let lessonId: Int
    let quizId: Int
    let startDate: Date?
    let endDate: Date?
    let instructions: String?
    let state: String
    
    enum CodingKeys: String, CodingKey {
        case lessonId = "lesson_id"
        case quizId = "quiz_id"
        case startDate = "start_date"
        case endDate = "end_date"
        case instructions, state
    }
}

// MARK: - Publish Quiz Request/Response
struct PublishQuizResponse: Codable {
    let success: Bool
    let message: String
    let data: PublishQuizData
    let traceId: String
    let requestId: String
}

struct PublishQuizData: Codable {
    let lessonId: Int
    let quizId: Int
    let publishedAt: Date
    let studentsNotified: Int
    
    enum CodingKeys: String, CodingKey {
        case lessonId = "lesson_id"
        case quizId = "quiz_id"
        case publishedAt = "published_at"
        case studentsNotified = "students_notified"
    }
}

struct UnpublishQuizResponse: Codable {
    let success: Bool
    let message: String
    let data: UnpublishQuizData
    let traceId: String
    let requestId: String
}

struct UnpublishQuizData: Codable {
    let lessonId: Int
    let quizId: Int
    let unpublishedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case lessonId = "lesson_id"
        case quizId = "quiz_id"
        case unpublishedAt = "unpublished_at"
    }
}

// MARK: - Grading Request/Response
struct PendingGradingResponse: Codable {
    let success: Bool
    let message: String
    let data: PendingGradingData
    let traceId: String
    let requestId: String
}

struct PendingGradingData: Codable {
    let attempts: [QuizAttempt]
    let totalCount: Int
    let totalPages: Int
    let currentPage: Int
    let hasNextPage: Bool
    let hasPreviousPage: Bool
}

struct AttemptDetailResponse: Codable {
    let success: Bool
    let message: String
    let data: AttemptDetail
    let traceId: String
    let requestId: String
}

struct GradeAttemptRequest: Codable {
    let attemptId: Int
    let answers: [GradeAnswerRequest]
    let feedback: String?
    
    enum CodingKeys: String, CodingKey {
        case attemptId = "attempt_id"
        case answers, feedback
    }
}

struct GradeAnswerRequest: Codable {
    let answerId: Int
    let score: Double
    let isCorrect: Bool
    let feedback: String?
    
    enum CodingKeys: String, CodingKey {
        case answerId = "answer_id"
        case score
        case isCorrect = "is_correct"
        case feedback
    }
}

struct GradeAttemptResponse: Codable {
    let success: Bool
    let message: String
    let data: GradeAttemptData
    let traceId: String
    let requestId: String
}

struct GradeAttemptData: Codable {
    let attemptId: Int
    let totalScore: Double
    let maxScore: Double
    let percentage: Double
    let isPassed: Bool
    let gradedAt: Date
    let gradedBy: String
    
    enum CodingKeys: String, CodingKey {
        case attemptId = "attempt_id"
        case totalScore = "total_score"
        case maxScore = "max_score"
        case percentage
        case isPassed = "is_passed"
        case gradedAt = "graded_at"
        case gradedBy = "graded_by"
    }
}

// MARK: - Quiz Attempts Request/Response
struct AttemptFilters: Codable {
    let state: AttemptState?
    let studentId: Int?
    let needsGrading: Bool?
    let search: String?
    
    init(state: AttemptState? = nil, studentId: Int? = nil, needsGrading: Bool? = nil, search: String? = nil) {
        self.state = state
        self.studentId = studentId
        self.needsGrading = needsGrading
        self.search = search
    }
}

struct QuizAttemptsResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizAttemptsData
    let traceId: String
    let requestId: String
}

struct QuizAttemptsData: Codable {
    let attempts: [QuizAttempt]
    let totalCount: Int
    let totalPages: Int
    let currentPage: Int
    let hasNextPage: Bool
    let hasPreviousPage: Bool
}

// MARK: - Quiz State Management Request/Response
struct QuizStateResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizStateData
    let traceId: String
    let requestId: String
}

struct QuizStateData: Codable {
    let quizId: Int
    let state: QuizState
    let updatedAt: Date
    let message: String

    enum CodingKeys: String, CodingKey {
        case quizId = "quiz_id"
        case state
        case updatedAt = "updated_at"
        case message
    }
}

// MARK: - Quiz Delete Request/Response
struct QuizDeleteResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizDeleteData
    let traceId: String
    let requestId: String
}

struct QuizDeleteData: Codable {
    let quizId: Int
    let deletedAt: Date
    let message: String

    enum CodingKeys: String, CodingKey {
        case quizId = "quiz_id"
        case deletedAt = "deleted_at"
        case message
    }
}
